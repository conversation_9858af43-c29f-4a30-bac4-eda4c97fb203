import React, { useEffect, useState, useRef } from 'react';
import { Dimensions, Platform, StyleSheet, Text, View, TouchableOpacity, ActivityIndicator } from 'react-native';
import { typography } from '@/components/ThemedText';
import { useGameContext } from '@/components/game/GameContext';
import { StatusBar } from 'react-native';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { ENTITLEMENT_ID } from '@/services/revenueCatService';
import LottieView from 'lottie-react-native';
import { useNavigation } from '@react-navigation/native';

import Animated, { FadeInDown, Easing, useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { useLanguage } from '@/services/i18nService';
import { useSound } from '@/components/game/playSound';
import * as Haptics from 'expo-haptics';
import { useAppSettings } from '@/context/AppSettingsContext';
import { useAdSettings } from '@/context/AdSettingsContext';
import { LinearGradient } from 'expo-linear-gradient';
import * as Sentry from '@sentry/react-native';

// Resources
import QuestionsIcon from '@/assets/redesign/icon_questions.svg';
import DaresIcon from '@/assets/redesign/icon_dares.svg';
import SelectedIcon from '@/assets/redesign/icon_selected.svg';
import SettingsIcon from '@/assets/redesign/icon_control_setting.svg';
import ArrowRight from '@/assets/redesign/icon_control_chevronRight.svg';

// What's New
import WhatsNewButton from '@/components/whatsnew/WhatsNewButton';
import { checkForNewLogs } from '@/services/whatsNewService';

// Network Toast
import ToastBanner from '@/components/notifications/ToastBanner';

SplashScreen.preventAutoHideAsync(); // Keep splash screen visible until assets are loaded

export default function HomeScreen() {
  const navigation = useNavigation();
  const { setGameMode } = useGameContext();
  const { t } = useLanguage();
  const [assetsLoaded, setAssetsLoaded] = useState(false);
  const [fontsLoaded] = useFonts({
    'Melindya': require('../assets/fonts/Melindya.ttf'),
    'Nunito-Bold': require('../assets/fonts/Nunito-Bold.ttf'),
    'Nunito-SemiBold': require('../assets/fonts/Nunito-SemiBold.ttf'),
  });
  const { playSound } = useSound();
  const { isHapticsOn, retrySetDefaultPenalties } = useAppSettings();
  const { unlockPremium } = useAppSettings();
  const { refreshAdSettings } = useAdSettings();
  const [selectedModes, setSelectedModes] = useState<('questions' | 'challenges')[]>([]);
  const [hasNewWhatsNewLogs, setHasNewWhatsNewLogs] = useState(false);

  // Loading state management for comprehensive API calls
  const [toastState, setToastState] = useState<'loading' | 'success' | 'error' | null>(null);
  const toastOpacity = useSharedValue(0); // Start with opacity 0 to prevent flash
  const [hasCachedContent, setHasCachedContent] = useState(false);
  const [uiOpacity, setUiOpacity] = useState(1);
  const [isInteractionDisabled, setIsInteractionDisabled] = useState(false);

  // Ref to track first mount status
  const isFirstMount = useRef<boolean>(true);

  const screenWidth = Dimensions.get('window').width;
  const aspectRatio = 473 / 1024;

  const isBottomSheetVisible = selectedModes.length > 0;

  const bottomSheetTranslateY = useSharedValue(200);

  const bottomSheetStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: bottomSheetTranslateY.value }],
  }));

  const toastAnimatedStyle = useAnimatedStyle(() => ({
    opacity: toastOpacity.value,
  }));

  // No-op clearTimeouts since we no longer use timeouts for toast
  const clearTimeouts = () => {};

  // Function to check cached content availability
  const checkCachedContentAvailability = async () => {
    try {
      const { getCachedGameContent } = require('@/services/contentService');
      const { loadPenaltyContent } = require('@/services/penaltyService');

      const cachedContent = await getCachedGameContent();
      const cachedPenalties = await loadPenaltyContent();

      // Check if we have meaningful cached content (not just empty structures)
      const hasContent = cachedContent &&
        cachedContent.questions &&
        cachedContent.dares && (
          Object.values(cachedContent.questions).some((arr: any) => Array.isArray(arr) && arr.length > 0) ||
          Object.values(cachedContent.dares).some((arr: any) => Array.isArray(arr) && arr.length > 0)
        );

      const hasPenalties = cachedPenalties &&
        Object.values(cachedPenalties).some((arr: any) => Array.isArray(arr) && arr.length > 0);

      console.log('📦 Cached content check:', {
        hasContent: !!hasContent,
        hasPenalties: !!hasPenalties,
        contentStructure: cachedContent ? {
          questionsKeys: Object.keys(cachedContent.questions || {}),
          daresKeys: Object.keys(cachedContent.dares || {}),
          totalQuestions: Object.values(cachedContent.questions || {}).reduce((sum: number, arr: any) => sum + (Array.isArray(arr) ? arr.length : 0), 0),
          totalDares: Object.values(cachedContent.dares || {}).reduce((sum: number, arr: any) => sum + (Array.isArray(arr) ? arr.length : 0), 0)
        } : null,
        penaltyStructure: cachedPenalties ? {
          keys: Object.keys(cachedPenalties),
          totalPenalties: Object.values(cachedPenalties).reduce((sum: number, arr: any) => sum + (Array.isArray(arr) ? arr.length : 0), 0)
        } : null
      });

      const hasCached = hasContent || hasPenalties;
      setHasCachedContent(hasCached);

      console.log(`📦 Final cached content availability: ${hasCached ? 'YES' : 'NO'}`);
      return hasCached;
    } catch (error) {
      console.error('Error checking cached content:', error);
      setHasCachedContent(false);
      return false;
    }
  };

  // Comprehensive API call function that coordinates all three services
  const performComprehensiveApiCalls = async (forceRefresh: boolean = false) => {
    try {
      console.log('🔄 Starting comprehensive API calls...');
      // Immediately show loading and block UI
      setToastState('loading');
      toastOpacity.value = withTiming(1, { duration: 350, easing: Easing.out(Easing.exp) }); // Make toast visible for loading state
      setUiOpacity(0.3);
      setIsInteractionDisabled(true);
      clearTimeouts();

      // Import required functions
      const { fetchAndUpdateGameContent } = require('@/services/contentService');
      const { fetchAndUpdatePenalties } = require('@/services/penaltyService');
      const { updatePoolItemsText } = require('@/services/poolStorageService');

      // Execute all four API calls concurrently
      const [contentResult, penaltyResult, whatsNewResult, adSettingsResult] = await Promise.allSettled([
        fetchAndUpdateGameContent(forceRefresh),
        fetchAndUpdatePenalties(forceRefresh),
        checkForNewLogs(),
        refreshAdSettings(forceRefresh) // Use context function instead of service directly
      ]);

      // Process results - distinguish between API success, throttling, and error
      // Add detailed logging for content validation
      if (contentResult.status === 'fulfilled') {
        console.log('🔍 Content validation details:', {
          status: contentResult.status,
          hasValue: contentResult.value !== null,
          hasContent: contentResult.value?.content !== null,
          fromCache: contentResult.value?.fromCache,
          isThrottled: contentResult.value?.isThrottled,
          hasQuestions: !!contentResult.value?.content?.questions,
          hasDares: !!contentResult.value?.content?.dares,
          questionCategories: contentResult.value?.content?.questions ? Object.keys(contentResult.value.content.questions) : [],
          dareCategories: contentResult.value?.content?.dares ? Object.keys(contentResult.value.content.dares) : []
        });
      } else {
        console.log('🔍 Content result failed:', {
          status: contentResult.status,
          reason: contentResult.reason
        });
      }

      const contentApiSuccess = contentResult.status === 'fulfilled' &&
        contentResult.value !== null &&
        contentResult.value.content !== null &&
        contentResult.value.apiSuccess && // Use the new apiSuccess flag
        contentResult.value.content.questions &&
        contentResult.value.content.dares && (
          Object.values(contentResult.value.content.questions).some((arr: any) => Array.isArray(arr) && arr.length > 0) ||
          Object.values(contentResult.value.content.dares).some((arr: any) => Array.isArray(arr) && arr.length > 0)
        );

      const contentThrottled = contentResult.status === 'fulfilled' &&
        contentResult.value !== null &&
        contentResult.value.content !== null &&
        contentResult.value.fromCache &&
        contentResult.value.isThrottled;

      // Add detailed logging for penalty validation
      if (penaltyResult.status === 'fulfilled') {
        console.log('🎯 Penalty validation details:', {
          status: penaltyResult.status,
          hasValue: penaltyResult.value !== null,
          hasContent: penaltyResult.value?.content !== null,
          fromCache: penaltyResult.value?.fromCache,
          isThrottled: penaltyResult.value?.isThrottled,
          contentKeys: penaltyResult.value?.content ? Object.keys(penaltyResult.value.content) : []
        });
      } else {
        console.log('🎯 Penalty result failed:', {
          status: penaltyResult.status,
          reason: penaltyResult.reason
        });
      }

      const penaltyApiSuccess = penaltyResult.status === 'fulfilled' &&
        penaltyResult.value !== null &&
        penaltyResult.value.content !== null &&
        penaltyResult.value.apiSuccess && // Use the new apiSuccess flag
        Object.values(penaltyResult.value.content || {}).some((arr: any) => Array.isArray(arr) && arr.length > 0);

      const penaltyThrottled = penaltyResult.status === 'fulfilled' &&
        penaltyResult.value !== null &&
        penaltyResult.value.content !== null &&
        penaltyResult.value.fromCache &&
        penaltyResult.value.isThrottled;

      // What's New is considered non-critical - it should not affect overall success
      // We only care if content and penalties are successful
      if (whatsNewResult.status === 'fulfilled') {
        setHasNewWhatsNewLogs(whatsNewResult.value.hasNewLogs);
        console.log('📰 What\'s New check completed:', whatsNewResult.value);
      } else {
        setHasNewWhatsNewLogs(false);
        console.log('📰 What\'s New check failed, but continuing anyway (non-critical)');
      }

      // Ad Settings is also considered non-critical - it should not affect overall success
      if (adSettingsResult.status === 'fulfilled') {
        console.log('⚙️ Ad Settings check completed successfully');
      } else {
        console.log('⚙️ Ad Settings check failed, but continuing anyway (non-critical)');
      }

      // If penalty content was successfully fetched, retry setting default penalties
      if ((penaltyApiSuccess || penaltyThrottled) && penaltyResult.status === 'fulfilled' && penaltyResult.value && penaltyResult.value.content) {
        try {
          console.log('🎯 Penalty content loaded, retrying default penalty setup...');
          await retrySetDefaultPenalties();
        } catch (penaltySetupError) {
          console.error('Error setting up default penalties:', penaltySetupError);
          Sentry.captureException(penaltySetupError);
        }
      }

      // If content was successfully fetched, update existing pools with fresh content
      if (contentApiSuccess && contentResult.status === 'fulfilled' && contentResult.value && contentResult.value.content) {
        try {
          console.log('🔄 Content successfully fetched, updating existing pools with fresh content...');
          await updatePoolItemsText(contentResult.value.content);
          console.log('✅ Pool items updated successfully with fresh content');
        } catch (poolUpdateError) {
          console.error('Error updating pool items with fresh content:', poolUpdateError);
          Sentry.captureException(poolUpdateError);
          // Don't fail the entire process if pool update fails
        }
      }

      // Determine the overall state
      const allApiSuccessful = contentApiSuccess && penaltyApiSuccess;
      const allThrottled = contentThrottled && penaltyThrottled;
      const someThrottled = contentThrottled || penaltyThrottled;

      if (allApiSuccessful) {
        // All API calls succeeded - show success toast
        console.log('✅ All API calls completed successfully');
        setToastState('success');
        toastOpacity.value = withTiming(1, { duration: 350, easing: Easing.out(Easing.exp) }); // Make toast visible for success state
        setUiOpacity(1);
        setIsInteractionDisabled(false);
        return true;
      } else if (allThrottled || (someThrottled && (contentApiSuccess || penaltyApiSuccess))) {
        // All throttled OR mixed throttled/success - no toast needed (normal behavior)
        console.log('⏱️ Content served from cache due to throttling - no toast needed');
        setToastState(null);
        toastOpacity.value = withTiming(0, { duration: 300, easing: Easing.in(Easing.exp) }); // Hide toast for throttled state
        setUiOpacity(1);
        setIsInteractionDisabled(false);
        return true;
      } else {
        // Some API calls failed (not due to throttling) - show error toast
        console.log('❌ Some API calls failed:', {
          contentApiSuccess,
          contentThrottled,
          penaltyApiSuccess,
          penaltyThrottled
        });
        // Re-check if we have meaningful cached content
        const hasCached = await checkCachedContentAvailability();
        setToastState('error');
        toastOpacity.value = withTiming(1, { duration: 350, easing: Easing.out(Easing.exp) }); // Make toast visible for error state
        if (hasCached) {
          setUiOpacity(1);
          setIsInteractionDisabled(false);
        } else {
          setUiOpacity(0.3);
          setIsInteractionDisabled(true);
        }
        return false;
      }
    } catch (error) {
      console.error('Error in comprehensive API calls:', error);
      Sentry.captureException(error);
      // On error, check for cache
      const hasCached = await checkCachedContentAvailability();
      // Refuerza aquí: SIEMPRE mostrar el toast de error tras fetch fallido,
      // incluso si ya había error antes, y aunque venga de caché, y aunque haya expirado throttle.
      setToastState('error');
      toastOpacity.value = withTiming(1, { duration: 350, easing: Easing.out(Easing.exp) }); // Make toast visible for error state
      if (hasCached) {
        setUiOpacity(1);
        setIsInteractionDisabled(false);
      } else {
        setUiOpacity(0.3);
        setIsInteractionDisabled(true);
      }
      return false;
    }
  };

  // Function to check for new What's New logs (simplified version)
  const checkWhatsNewLogs = async () => {
    try {
      console.log('HOME: Checking for new What\'s New logs...');
      const { hasNewLogs } = await checkForNewLogs();
      setHasNewWhatsNewLogs(hasNewLogs);
      console.log('HOME: Has new What\'s New logs:', hasNewLogs);
    } catch (error) {
      console.error('HOME: Error checking What\'s New logs:', error);
      Sentry.captureException(error);
      setHasNewWhatsNewLogs(false);
    }
  };

  // Function to handle toast banner retry
  const handleToastRetry = async () => {
    console.log('🔄 Toast retry triggered');
    await performComprehensiveApiCalls(true);
  };

  // Function to handle toast close (for error with cached content)
  const handleToastBannerClose = () => {
    // Fade out the toast smoothly
    toastOpacity.value = withTiming(0, { duration: 300, easing: Easing.in(Easing.exp) });

    // After the fade-out animation completes, hide the toast completely
    setTimeout(() => {
      setToastState(null);
    }, 300);

    setUiOpacity(1);
    setIsInteractionDisabled(false);
  };

  // Function to handle UI element tap when error banner is shown and user has cached content
  const handleUiElementTap = () => {
    if (toastState === 'error' && hasCachedContent) {
      handleToastBannerClose();
    }
  };

  // Effect to load assets and refresh content only when the component mounts (app launch)
  useEffect(() => {
    async function loadAssets() {
      try {
        // Wait to ensure sandbox receipt updates
        await new Promise(res => setTimeout(res, 1500));
        const Purchases = require('react-native-purchases').default;
        const customerInfo = await Purchases.getCustomerInfo();
        console.log('🧾 All entitlements:', customerInfo.entitlements.active);
        const entitlement = customerInfo.entitlements.active[ENTITLEMENT_ID];
        const hasAccess = entitlement?.isActive ?? false;
        console.log('🔍 Subscription check on startup:', hasAccess);
        if (hasAccess) {
          unlockPremium();
        }

        // Wait a bit so LottieView has time to mount and avoids flicker
        await new Promise(resolve => {
          const timer = setTimeout(() => {
            clearTimeout(timer);
            resolve(undefined);
          }, 1000);
        });

        setAssetsLoaded(true);
        await SplashScreen.hideAsync(); // Only hide now after everything is ready

        // Only refresh game content on first mount (app launch)
        // This prevents unnecessary API calls when navigating back to HomeScreen
        if (isFirstMount.current) {
          console.log('HOME: Initial app launch, performing comprehensive API calls...');
          performComprehensiveApiCalls();
          isFirstMount.current = false;
        } else {
          console.log('HOME: Returning to HomeScreen, skipping content refresh');
          // Still check for new What's New logs when returning to home
          checkWhatsNewLogs();
        }
      } catch (error) {
        console.error('Error loading assets:', error);
        Sentry.captureException(error);
        await SplashScreen.hideAsync();
      }
    }

    loadAssets();
  }, []);

  // We no longer refresh content every time the screen gets focus
  // This improves performance by reducing unnecessary API calls
  // Content is now only refreshed when the app launches

  useEffect(() => {
    bottomSheetTranslateY.value = withTiming(isBottomSheetVisible ? 0 : 200, {
      duration: 300,
      easing: Easing.out(Easing.exp),
    });
  }, [isBottomSheetVisible]);

  // Cleanup effect (no timeouts to clear anymore)
  useEffect(() => {
    return () => {
      clearTimeouts();
    };
  }, []);

  if (!fontsLoaded || !assetsLoaded) {
    return (
      <View style={{
        flex:1,
        backgroundColor: '#131416',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <ActivityIndicator size="large" color="#F3EBDC" />
        <Text style={[typography.heading4, { color: '#F3EBDC', marginTop: 16 }]}>{t('loadingAssets')}</Text>
      </View>)

  }

  const toggleModeSelection = async (mode: 'questions' | 'challenges') => {
    // Handle UI element tap for error dismissal if applicable
    handleUiElementTap();

    // Prevent interaction if disabled
    if (isInteractionDisabled) return;

    await playSound('tapEffect3');
    setSelectedModes(prev =>
      prev.includes(mode) ? prev.filter(m => m !== mode) : [...prev, mode]
    );
    if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getFadeInAnimation = (delay: number) =>
      FadeInDown.duration(400).delay(delay).easing(Easing.bounce);

  return (
    <View
      style={styles.mainContainer}
    >
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content" // Makes icons and text white
      />

      <TouchableOpacity
        style={[styles.settingsButton, { opacity: uiOpacity }]}
        onPress={async () => {
          handleUiElementTap();
          if (isInteractionDisabled) return;

          await playSound('tapEffect1');
          if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          navigation.navigate('Settings' as never);
        }}
        disabled={isInteractionDisabled}
      >
        <SettingsIcon width={48} height={48} />
      </TouchableOpacity>

      <Animated.View
        entering={getFadeInAnimation(100)}
        style={[styles.logo]}>
          {assetsLoaded && (
            <LottieView
              autoPlay
              loop={false} // This stops it from looping
              speed={2}
              style={{
                width: 409,
                height: 231,
                transform: [{translateY: -72}],
                opacity: uiOpacity,
              }}
              source={require('../assets/lottie/logo_intro.json')}
            />
          )}
      </Animated.View>

      <View style={[styles.buttonStack, { opacity: uiOpacity }]}>
        {/* CHALLENGES ENTRY POINT */}
        <Animated.View
            entering={getFadeInAnimation(300)}
        >
          <TouchableOpacity
            onPress={() => toggleModeSelection('challenges')}
            style={[styles.button, selectedModes.includes('challenges') && styles.buttonSelected]}
            disabled={isInteractionDisabled}
          >
              <Text style={[typography.heading3, styles.buttonText, selectedModes.includes('challenges') && styles.buttonTextSelected]}>{t('challengesIndex')}</Text>
              {selectedModes.includes('challenges') ? (
                <SelectedIcon style={styles.buttomImageSelected}/>
              ) : (
                <DaresIcon style={styles.buttomImage}/>
              )}
          </TouchableOpacity>
        </Animated.View>

        {/* QUESTIONS ENTRY POINT */}
        <Animated.View
            entering={getFadeInAnimation(500)}
        >
          <TouchableOpacity
            onPress={() => toggleModeSelection('questions')}
            style={[styles.button, selectedModes.includes('questions') && styles.buttonSelected]}
            disabled={isInteractionDisabled}
          >
            <Text style={[typography.heading3, styles.buttonText, selectedModes.includes('questions') && styles.buttonTextSelected]}>{t('questionsIndex')}</Text>
            {selectedModes.includes('questions') ? (
              <SelectedIcon style={styles.buttomImageSelected}/>
            ) : (
              <QuestionsIcon style={styles.buttomImage}/>
            )}
          </TouchableOpacity>
        </Animated.View>

        {/* What's New Button */}
        <Animated.View
            entering={getFadeInAnimation(700)}
        >
          <WhatsNewButton
            onPress={async () => {
              handleUiElementTap();
              if (isInteractionDisabled) return;

              await playSound('tapEffect1');
              if (isHapticsOn) Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              navigation.navigate('WhatsNewScreen' as never);
            }}
            showDot={hasNewWhatsNewLogs}
          />
        </Animated.View>
      </View>

      {/* Dynamic Toast Banner */}
      {toastState && (
        <Animated.View
          style={[styles.toastBanner, toastAnimatedStyle]}
        >
          <ToastBanner
            state={toastState}
            onPress={toastState === 'error' ? handleToastRetry : undefined}
            showCloseButton={toastState !== 'error' || hasCachedContent}
            onClose={toastState !== 'error' || hasCachedContent ? handleToastBannerClose : undefined}
          />
        </Animated.View>
      )}

      {/* LOTTIE BOTTOM */}
      <Animated.View
        style={{ width: screenWidth, height: screenWidth * aspectRatio}}
        entering={getFadeInAnimation(200)}
        >
        <LottieView
            autoPlay
            style={[styles.cloudLoop, { opacity: uiOpacity }]}
            source={require('../assets/lottie/cloud_loop.json')}
          />
      </Animated.View>

      {/* CONTINUE BUTTON */}
      <View style={StyleSheet.absoluteFill} pointerEvents="box-none">
        {/* Only the bottom sheet will catch touches */}
        <View style={{ flex: 1 }} pointerEvents="none" />

        <Animated.View
          style={[styles.bottomSheetAnimated, bottomSheetStyle]}
        >
          <TouchableOpacity
            onPress={() => {
              setGameMode(selectedModes as any);
              navigation.navigate('SelectCategories' as never);
            }}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#42E0FF', '#42FFB7']}
              style={styles.bottomSheet}
              pointerEvents="auto"
            >
              <Text style={[typography.heading3, styles.bottomSheetText]}>{selectedModes.length} {t('selectedOption')}</Text>
              <ArrowRight width={64} height={64}/>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    backgroundColor: '#131416'
  },
  settingsButton: {
    zIndex: 999,
    position: 'absolute',
    top: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 8,
    left: 8,
    padding: 16,
  },
  logo: {
    marginTop: (Platform.OS === 'android' ? StatusBar.currentHeight ?? 0 : 54) + 120,
    marginBottom: 64,
    height: 74
  },
  spanishStyle: {
    maxWidth: 260,
  },
  buttonStack: {
    width: '100%',
    maxWidth: 280,
    alignItems: 'center',
    minHeight: 365,
    paddingHorizontal: 28,
    gap: 42,
    flex: 1,
  },
  button: {
    width: 280,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3EBDC',
    borderRadius: 28,
    height: 96,
  },
  buttonText: {
    top: 4,
    color: '#131416',
    fontSize: 41,
    lineHeight: 48,
  },
  buttomImage: {
    position: 'absolute',
    top: -32,
    left: -38,
    width: 100,
    height: 100,
  },
  buttonSelected: {
    borderColor: '#43F5CC',
    borderWidth: 8,
    borderStyle: 'solid',
    backgroundColor: 'transparent',
  },
  buttonTextSelected: {
    color: '#43F5CC',
  },
  buttomImageSelected: {
    position: 'absolute',
    top: -44,
    left: -48,
    width: 100,
    height: 100,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    paddingTop: 20,
    paddingLeft: 28,
    paddingRight: 28,
    paddingBottom: 28,
    minHeight: 140,
  },
  bottomSheetText: {
    top: 4,
    color: '#131416',
    fontSize: 41,
    lineHeight: 48,
  },
  bottomSheetAnimated: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  cloudLoop: {
    width: '100%',
    height: '120%'
  },
  toastBanner: {
    zIndex: 999,
    position: 'absolute',
    bottom: 48,
    alignSelf: 'center',
  },
});